import { useState, useCallback, useRef, useEffect } from 'react';

// Types
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  quickReplies?: string[];
  metadata?: {
    intent?: string;
    confidence?: number;
    escalated?: boolean;
  };
}

export interface ChatSession {
  id: string;
  status: 'active' | 'ended' | 'escalated';
  createdAt: Date;
}

interface UseChatbotState {
  session: ChatSession | null;
  messages: ChatMessage[];
  loading: boolean;
  error: string | null;
  isTyping: boolean;
  isConnected: boolean;
}

interface ChatContext {
  userName?: string;
  location?: string;
  currentOrder?: string;
  preferredLanguage?: 'en' | 'fil';
}

// Main chatbot hook
export function useChatbot() {
  const [state, setState] = useState<UseChatbotState>({
    session: null,
    messages: [],
    loading: false,
    error: null,
    isTyping: false,
    isConnected: false,
  });

  const contextRef = useRef<ChatContext>({});
  const retryCountRef = useRef(0);
  const maxRetries = 3;

  // Update state helper
  const updateState = useCallback((updates: Partial<UseChatbotState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // Start new chat session
  const startChat = useCallback(async (userId?: string, initialContext?: ChatContext) => {
    updateState({ loading: true, error: null });

    try {
      const response = await fetch('/api/chatbot/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          context: initialContext,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to start chat session');
      }

      // Update context
      if (initialContext) {
        contextRef.current = { ...contextRef.current, ...initialContext };
      }

      updateState({
        session: data.session,
        messages: [data.welcomeMessage],
        isConnected: true,
        loading: false,
      });

      retryCountRef.current = 0;
      return data.session;

    } catch (error) {
      console.error('Error starting chat:', error);
      updateState({
        error: error instanceof Error ? error.message : 'Failed to start chat',
        loading: false,
        isConnected: false,
      });
      throw error;
    }
  }, [updateState]);

  // Send message
  const sendMessage = useCallback(async (message: string, additionalContext?: ChatContext) => {
    if (!state.session) {
      throw new Error('No active chat session. Please start a new chat.');
    }

    if (!message.trim()) {
      return;
    }

    // Add user message immediately for better UX
    const userMessage: ChatMessage = {
      id: `temp_${Date.now()}`,
      role: 'user',
      content: message.trim(),
      timestamp: new Date(),
    };

    updateState({
      messages: [...state.messages, userMessage],
      isTyping: true,
      error: null,
    });

    try {
      // Merge context
      const fullContext = { ...contextRef.current, ...additionalContext };

      const response = await fetch('/api/chatbot/message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: state.session.id,
          message: message.trim(),
          context: fullContext,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send message');
      }

      // Update messages with actual IDs and AI response
      updateState({
        messages: prev => [
          ...prev.slice(0, -1), // Remove temp user message
          { ...userMessage, id: `user_${Date.now()}` }, // Add real user message
          data.response, // Add AI response
        ],
        isTyping: false,
        session: prev => prev ? { ...prev, status: data.sessionStatus } : null,
      });

      retryCountRef.current = 0;
      return data.response;

    } catch (error) {
      console.error('Error sending message:', error);
      
      // Remove the temporary user message on error
      updateState({
        messages: prev => prev.slice(0, -1),
        isTyping: false,
        error: error instanceof Error ? error.message : 'Failed to send message',
      });

      // Retry logic
      if (retryCountRef.current < maxRetries) {
        retryCountRef.current++;
        setTimeout(() => {
          sendMessage(message, additionalContext);
        }, 1000 * retryCountRef.current);
      }

      throw error;
    }
  }, [state.session, state.messages, updateState]);

  // Send quick reply
  const sendQuickReply = useCallback(async (reply: string) => {
    return sendMessage(reply);
  }, [sendMessage]);

  // Update context
  const updateContext = useCallback((newContext: Partial<ChatContext>) => {
    contextRef.current = { ...contextRef.current, ...newContext };
  }, []);

  // Clear chat
  const clearChat = useCallback(() => {
    updateState({
      session: null,
      messages: [],
      error: null,
      isTyping: false,
      isConnected: false,
    });
    contextRef.current = {};
    retryCountRef.current = 0;
  }, [updateState]);

  // End chat session
  const endChat = useCallback(() => {
    if (state.session) {
      updateState({
        session: { ...state.session, status: 'ended' },
        isConnected: false,
      });
    }
  }, [state.session, updateState]);

  // Get last AI message
  const getLastAIMessage = useCallback(() => {
    if (!Array.isArray(state.messages)) return null;
    const aiMessages = state.messages.filter(msg => msg.role === 'assistant');
    return aiMessages[aiMessages.length - 1] || null;
  }, [state.messages]);

  // Check if escalated
  const isEscalated = useCallback(() => {
    return state.session?.status === 'escalated';
  }, [state.session]);

  // Auto-cleanup on unmount
  useEffect(() => {
    return () => {
      // Cleanup can be added here if needed
    };
  }, []);

  return {
    // State
    session: state.session,
    messages: Array.isArray(state.messages) ? state.messages : [],
    loading: state.loading,
    error: state.error,
    isTyping: state.isTyping,
    isConnected: state.isConnected,
    isEscalated: isEscalated(),

    // Actions
    startChat,
    sendMessage,
    sendQuickReply,
    updateContext,
    clearChat,
    endChat,

    // Helpers
    getLastAIMessage,
    context: contextRef.current,
  };
}

// Specialized hook for customer support chat
export function useCustomerSupportChat() {
  const chatbot = useChatbot();

  const startSupportChat = useCallback(async (
    customerInfo?: {
      name?: string;
      email?: string;
      orderId?: string;
      location?: string;
    }
  ) => {
    const context: ChatContext = {
      userName: customerInfo?.name,
      location: customerInfo?.location,
      currentOrder: customerInfo?.orderId,
    };

    return chatbot.startChat(customerInfo?.email, context);
  }, [chatbot]);

  const reportOrderIssue = useCallback(async (orderId: string, issue: string) => {
    chatbot.updateContext({ currentOrder: orderId });
    return chatbot.sendMessage(`I have an issue with my order ${orderId}: ${issue}`);
  }, [chatbot]);

  const askAboutDelivery = useCallback(async (location: string) => {
    chatbot.updateContext({ location });
    return chatbot.sendMessage(`What are the delivery options for ${location}?`);
  }, [chatbot]);

  return {
    ...chatbot,
    startSupportChat,
    reportOrderIssue,
    askAboutDelivery,
  };
}
