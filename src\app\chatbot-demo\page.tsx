'use client';

import React, { useState } from 'react';
import ChatWidget from '@/components/chatbot/ChatWidget';

export default function ChatbotDemoPage() {
  const [customerInfo, setCustomerInfo] = useState({
    name: '<PERSON>',
    email: '<EMAIL>',
    orderId: 'TAP2GO-12345',
    location: 'Makati City',
  });

  const [widgetSettings, setWidgetSettings] = useState({
    position: 'bottom-right' as 'bottom-right' | 'bottom-left',
    theme: 'orange' as 'orange' | 'blue' | 'green',
    showWelcomeMessage: true,
    autoOpen: false,
  });

  const demoScenarios = [
    {
      title: "Order Tracking",
      description: "Customer wants to track their order",
      customerInfo: {
        name: "<PERSON> Santos",
        email: "<EMAIL>",
        orderId: "TAP2GO-67890",
        location: "BGC, Taguig"
      },
      suggestedQuestions: [
        "Where is my order?",
        "How long until my food arrives?",
        "Can I track my delivery?"
      ]
    },
    {
      title: "Restaurant Discovery",
      description: "Customer looking for restaurants",
      customerInfo: {
        name: "<PERSON>",
        email: "<EMAIL>",
        location: "Ortigas, Pasig"
      },
      suggestedQuestions: [
        "What restaurants are open near me?",
        "I want pizza delivery",
        "Show me Filipino restaurants"
      ]
    },
    {
      title: "Payment Issues",
      description: "Customer having payment problems",
      customerInfo: {
        name: "Anna Lim",
        email: "<EMAIL>",
        orderId: "TAP2GO-11111",
        location: "Quezon City"
      },
      suggestedQuestions: [
        "My payment failed",
        "How do I get a refund?",
        "What payment methods do you accept?"
      ]
    },
    {
      title: "General Inquiry",
      description: "New customer with questions",
      customerInfo: {
        name: "Michael Chen",
        email: "<EMAIL>",
        location: "Manila"
      },
      suggestedQuestions: [
        "How does Tap2Go work?",
        "What are your delivery fees?",
        "Do you deliver to my area?"
      ]
    }
  ];

  const handleScenarioSelect = (scenario: typeof demoScenarios[0]) => {
    setCustomerInfo(scenario.customerInfo);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              🤖 Tap2Go AI Chatbot Demo
            </h1>
            <p className="text-lg text-gray-600">
              Experience our intelligent customer service assistant powered by Google AI Studio
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Demo Controls */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Demo Controls</h2>
              
              {/* Customer Info */}
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-700 mb-3">Customer Information</h3>
                <div className="space-y-3">
                  <input
                    type="text"
                    placeholder="Customer Name"
                    value={customerInfo.name}
                    onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                  <input
                    type="email"
                    placeholder="Email"
                    value={customerInfo.email}
                    onChange={(e) => setCustomerInfo(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                  <input
                    type="text"
                    placeholder="Order ID (optional)"
                    value={customerInfo.orderId}
                    onChange={(e) => setCustomerInfo(prev => ({ ...prev, orderId: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                  <input
                    type="text"
                    placeholder="Location"
                    value={customerInfo.location}
                    onChange={(e) => setCustomerInfo(prev => ({ ...prev, location: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                </div>
              </div>

              {/* Widget Settings */}
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-700 mb-3">Widget Settings</h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">Position</label>
                    <select
                      value={widgetSettings.position}
                      onChange={(e) => setWidgetSettings(prev => ({ ...prev, position: e.target.value as any }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="bottom-right">Bottom Right</option>
                      <option value="bottom-left">Bottom Left</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">Theme</label>
                    <select
                      value={widgetSettings.theme}
                      onChange={(e) => setWidgetSettings(prev => ({ ...prev, theme: e.target.value as any }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="orange">Orange (Tap2Go)</option>
                      <option value="blue">Blue</option>
                      <option value="green">Green</option>
                    </select>
                  </div>

                  <div className="flex items-center space-x-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={widgetSettings.showWelcomeMessage}
                        onChange={(e) => setWidgetSettings(prev => ({ ...prev, showWelcomeMessage: e.target.checked }))}
                        className="mr-2"
                      />
                      <span className="text-xs text-gray-600">Welcome Message</span>
                    </label>
                    
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={widgetSettings.autoOpen}
                        onChange={(e) => setWidgetSettings(prev => ({ ...prev, autoOpen: e.target.checked }))}
                        className="mr-2"
                      />
                      <span className="text-xs text-gray-600">Auto Open</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Demo Scenarios */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Demo Scenarios</h2>
              <div className="space-y-4">
                {demoScenarios.map((scenario, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium text-gray-900">{scenario.title}</h3>
                      <button
                        onClick={() => handleScenarioSelect(scenario)}
                        className="px-3 py-1 bg-orange-500 text-white text-xs rounded hover:bg-orange-600 transition-colors"
                      >
                        Use This
                      </button>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{scenario.description}</p>
                    <div className="space-y-1">
                      <p className="text-xs font-medium text-gray-700">Try asking:</p>
                      {scenario.suggestedQuestions.map((question, qIndex) => (
                        <p key={qIndex} className="text-xs text-gray-600">• {question}</p>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Demo Content */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border p-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">Chatbot Features</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                      <span className="text-orange-600 font-bold text-sm">🎯</span>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">Smart Intent Detection</h3>
                      <p className="text-sm text-gray-600">Automatically understands customer needs and provides relevant responses</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                      <span className="text-orange-600 font-bold text-sm">📱</span>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">Order Tracking</h3>
                      <p className="text-sm text-gray-600">Real-time order status updates and delivery information</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                      <span className="text-orange-600 font-bold text-sm">🏪</span>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">Restaurant Discovery</h3>
                      <p className="text-sm text-gray-600">Help customers find restaurants and menu items</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                      <span className="text-orange-600 font-bold text-sm">💳</span>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">Payment Support</h3>
                      <p className="text-sm text-gray-600">Assistance with payment methods, refunds, and billing issues</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                      <span className="text-orange-600 font-bold text-sm">🚀</span>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">Smart Escalation</h3>
                      <p className="text-sm text-gray-600">Automatically escalates complex issues to human support</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                      <span className="text-orange-600 font-bold text-sm">🌏</span>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">Filipino-Friendly</h3>
                      <p className="text-sm text-gray-600">Understands local context and Filipino-English communication</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
                <h3 className="font-medium text-orange-800 mb-2">🚀 Ready to Test!</h3>
                <p className="text-orange-700 text-sm mb-4">
                  Click the chat widget in the bottom-right corner to start a conversation. 
                  Try different scenarios and see how our AI assistant responds!
                </p>
                <div className="flex flex-wrap gap-2">
                  <span className="px-2 py-1 bg-orange-200 text-orange-800 text-xs rounded">24/7 Available</span>
                  <span className="px-2 py-1 bg-orange-200 text-orange-800 text-xs rounded">Instant Responses</span>
                  <span className="px-2 py-1 bg-orange-200 text-orange-800 text-xs rounded">Context Aware</span>
                  <span className="px-2 py-1 bg-orange-200 text-orange-800 text-xs rounded">Human Escalation</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Chat Widget */}
      <ChatWidget
        customerInfo={customerInfo}
        position={widgetSettings.position}
        theme={widgetSettings.theme}
        showWelcomeMessage={widgetSettings.showWelcomeMessage}
        autoOpen={widgetSettings.autoOpen}
      />
    </div>
  );
}
